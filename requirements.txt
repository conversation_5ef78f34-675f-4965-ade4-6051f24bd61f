# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Firebase Admin SDK
firebase-admin==6.2.0

# Google Cloud services
google-cloud-firestore==2.13.1
google-cloud-storage==2.10.0
googlemaps==4.10.0
google-api-python-client==2.108.0
google-auth==2.23.4
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1

# Email services
sendgrid==6.10.0
emails==0.6.0

# HTTP requests
httpx==0.25.2
requests==2.31.0

# Authentication and security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Environment variables
python-dotenv==1.0.0

# CORS middleware
fastapi-cors==0.0.6

# Data validation
pydantic==2.5.0
pydantic-settings==2.1.0

# Date and time handling
python-dateutil==2.8.2

# Logging
loguru==0.7.2

# Testing (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
