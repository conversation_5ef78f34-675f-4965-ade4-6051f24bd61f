{"indexes": [{"collectionGroup": "hackathons", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "start_date", "order": "ASCENDING"}]}, {"collectionGroup": "hackathons", "queryScope": "COLLECTION", "fields": [{"fieldPath": "organizer_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "teams", "queryScope": "COLLECTION", "fields": [{"fieldPath": "hackathon_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "submissions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "hackathon_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "role", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}], "fieldOverrides": []}