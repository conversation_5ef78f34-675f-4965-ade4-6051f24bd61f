# Firebase Configuration
REACT_APP_FIREBASE_API_KEY=AIzaSyAiEpm1nAGtA02XE2YSAK5kB1raJxYZOLM
REACT_APP_FIREBASE_AUTH_DOMAIN=hackhub-d80ed.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=hackhub-d80ed
REACT_APP_FIREBASE_STORAGE_BUCKET=hackhub-d80ed.firebasestorage.app
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=796925165887
REACT_APP_FIREBASE_APP_ID=1:796925165887:web:e3cf95f0aa4f340e02938a

# Google Maps API
REACT_APP_GOOGLE_MAPS_API_KEY=AIzaSyDJPb-NIqwYZr8zpS9aBcD38ZPxU_e5mDo

# FastAPI Backend URL
REACT_APP_API_URL=http://localhost:8000

# Server Environment Variables
FIREBASE_ADMIN_SDK_PATH=path/to/firebase-admin-sdk.json
GOOGLE_MAPS_API_KEY=AIzaSyDJPb-NIqwYZr8zpS9aBcD38ZPxU_e5mDo
EMAIL_SERVICE_API_KEY=your_email_service_api_key
EMAIL_FROM_ADDRESS=<EMAIL>

# GCP Configuration
GOOGLE_CLOUD_PROJECT=hackhub-463514
GOOGLE_APPLICATION_CREDENTIALS=path/to/gcp-service-account.json

# Database
DATABASE_URL=your_database_url_if_needed

# Gemini AI API (Optional - for future AI features)
GEMINI_API_KEY=AIzaSyCBv8jNE-5K8Ojs0UumdeBL_Zba68b4e18

# Security
JWT_SECRET_KEY=your_jwt_secret_key
CORS_ORIGINS=http://localhost:3000,https://your-domain.com
