{"name": "hackhub", "version": "1.0.0", "description": "Hackathon organization platform with dual interfaces for organizers and participants", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "client": "cd client && npm start", "server": "cd server && uvicorn main:app --reload --host 0.0.0.0 --port 8000", "build": "cd client && npm run build", "install-all": "npm install && cd client && npm install && cd ../server && pip install -r requirements.txt", "deploy": "gcloud app deploy"}, "keywords": ["hackathon", "react", "<PERSON><PERSON><PERSON>", "firebase", "tailwind"], "author": "HackHub Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}