import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp } from 'lucide-react';
import { motion } from 'framer-motion';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

const AnalyticsModal = ({ isOpen, onClose, hackathons }) => {
  if (!isOpen) return null;

  // Prepare data for charts
  const statusData = hackathons.reduce((acc, hackathon) => {
    const status = hackathon.status;
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {});

  const statusChartData = Object.entries(statusData).map(([status, count]) => ({
    status: status.charAt(0).toUpperCase() + status.slice(1),
    count
  }));

  const registrationData = hackathons.map(hackathon => ({
    name: hackathon.title.length > 15 ? hackathon.title.substring(0, 15) + '...' : hackathon.title,
    registrations: hackathon.participants?.length || 0
  }));

  const monthlyData = hackathons.reduce((acc, hackathon) => {
    const month = new Date(hackathon.created_at).toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    acc[month] = (acc[month] || 0) + 1;
    return acc;
  }, {});

  const monthlyChartData = Object.entries(monthlyData).map(([month, count]) => ({
    month,
    hackathons: count
  }));

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'];

  const totalRegistrations = hackathons.reduce((sum, h) => sum + (h.participants?.length || 0), 0);
  const avgRegistrations = hackathons.length > 0 ? Math.round(totalRegistrations / hackathons.length) : 0;
  const mostPopular = hackathons.reduce((max, h) => 
    (h.participants?.length || 0) > (max.participants?.length || 0) ? h : max, hackathons[0] || {});

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white rounded-xl shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-primary-600" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="card text-center">
              <div className="text-2xl font-bold text-primary-600">{hackathons.length}</div>
              <div className="text-sm text-gray-600">Total Hackathons</div>
            </div>
            <div className="card text-center">
              <div className="text-2xl font-bold text-green-600">{totalRegistrations}</div>
              <div className="text-sm text-gray-600">Total Registrations</div>
            </div>
            <div className="card text-center">
              <div className="text-2xl font-bold text-orange-600">{avgRegistrations}</div>
              <div className="text-sm text-gray-600">Avg. Registrations</div>
            </div>
            <div className="card text-center">
              <div className="text-2xl font-bold text-purple-600">
                {mostPopular.participants?.length || 0}
              </div>
              <div className="text-sm text-gray-600">Most Popular Event</div>
            </div>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Status Distribution */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <PieChart className="w-5 h-5 mr-2" />
                Hackathon Status Distribution
              </h3>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Pie
                    data={statusChartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ status, count }) => `${status}: ${count}`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {statusChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </RechartsPieChart>
              </ResponsiveContainer>
            </div>

            {/* Registration Numbers */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Registrations per Hackathon
              </h3>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={registrationData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="name" 
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    fontSize={12}
                  />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="registrations" fill="#3B82F6" />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Monthly Trends */}
            <div className="card lg:col-span-2">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                Monthly Hackathon Creation Trends
              </h3>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={monthlyChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="hackathons" fill="#10B981" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Top Performing Hackathons */}
          <div className="card mt-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Hackathons</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Hackathon
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Registrations
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {hackathons
                    .sort((a, b) => (b.participants?.length || 0) - (a.participants?.length || 0))
                    .slice(0, 5)
                    .map((hackathon) => (
                      <tr key={hackathon.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {hackathon.title}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {hackathon.participants?.length || 0}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            hackathon.status === 'upcoming' ? 'bg-blue-100 text-blue-800' :
                            hackathon.status === 'ongoing' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {hackathon.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(hackathon.start_date).toLocaleDateString()}
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default AnalyticsModal;
