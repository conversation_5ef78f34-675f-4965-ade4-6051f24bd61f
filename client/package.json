{"name": "hackhub-client", "version": "0.1.0", "private": true, "dependencies": {"@google-cloud/firestore": "^7.1.0", "@googlemaps/react-wrapper": "^1.1.35", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "firebase": "^10.7.1", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-confetti": "^6.4.0", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.4.1", "react-modal": "^3.16.1", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "recharts": "^2.15.3", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6"}, "proxy": "http://localhost:8000"}