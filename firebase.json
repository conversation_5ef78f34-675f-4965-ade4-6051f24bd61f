{"hosting": {"public": "client/build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": {"source": "server", "runtime": "python39"}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true}}}